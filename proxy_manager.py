import requests
import random
import time
import threading
from urllib.parse import urljoin
from urllib.request import Proxy<PERSON><PERSON><PERSON>, build_opener, install_opener
import logging

class ProxyManager:
    def __init__(self):
        self.proxies = []
        self.current_proxy = None
        self.failed_proxies = set()
        self.lock = threading.Lock()
        self.auto_switch = False
        self.max_retries = 3
        
        # 配置日志
        logging.basicConfig(level=logging.INFO)
        self.logger = logging.getLogger(__name__)
    
    def fetch_free_proxies(self):
        """从免费代理网站获取代理列表"""
        proxy_sources = [
            'https://www.proxy-list.download/api/v1/get?type=http',
            'https://api.proxyscrape.com/v2/?request=get&protocol=http&timeout=10000&country=all'
        ]
        
        new_proxies = []
        for source in proxy_sources:
            try:
                response = requests.get(source, timeout=10)
                if response.status_code == 200:
                    proxy_list = response.text.strip().split('\n')
                    for proxy in proxy_list:
                        if ':' in proxy:
                            new_proxies.append(proxy.strip())
            except Exception as e:
                self.logger.warning(f"获取代理失败 {source}: {e}")
        
        with self.lock:
            self.proxies = list(set(new_proxies))
            self.failed_proxies.clear()
        
        self.logger.info(f"获取到 {len(self.proxies)} 个代理")
        return len(self.proxies)
    
    def test_proxy(self, proxy, test_url='http://httpbin.org/ip', timeout=10):
        """测试代理是否可用"""
        try:
            proxies = {
                'http': f'http://{proxy}',
                'https': f'http://{proxy}'
            }
            response = requests.get(test_url, proxies=proxies, timeout=timeout)
            return response.status_code == 200
        except:
            return False
    
    def get_working_proxy(self):
        """获取一个可用的代理"""
        with self.lock:
            available_proxies = [p for p in self.proxies if p not in self.failed_proxies]
        
        if not available_proxies:
            self.logger.warning("没有可用代理，重新获取...")
            self.fetch_free_proxies()
            available_proxies = self.proxies
        
        for proxy in random.sample(available_proxies, min(5, len(available_proxies))):
            if self.test_proxy(proxy):
                self.current_proxy = proxy
                self.logger.info(f"找到可用代理: {proxy}")
                return proxy
            else:
                with self.lock:
                    self.failed_proxies.add(proxy)
        
        return None
    
    def switch_proxy(self):
        """手动切换代理"""
        old_proxy = self.current_proxy
        new_proxy = self.get_working_proxy()
        if new_proxy:
            self.logger.info(f"代理切换: {old_proxy} -> {new_proxy}")
            return new_proxy
        else:
            self.logger.error("无法找到可用代理")
            return None
    
    def make_request(self, url, method='GET', **kwargs):
        """使用代理发送请求，支持自动重试和切换"""
        for attempt in range(self.max_retries):
            if not self.current_proxy:
                self.current_proxy = self.get_working_proxy()
            
            if not self.current_proxy:
                raise Exception("无可用代理")
            
            try:
                proxies = {
                    'http': f'http://{self.current_proxy}',
                    'https': f'http://{self.current_proxy}'
                }
                
                if method.upper() == 'GET':
                    response = requests.get(url, proxies=proxies, timeout=10, **kwargs)
                elif method.upper() == 'POST':
                    response = requests.post(url, proxies=proxies, timeout=10, **kwargs)
                else:
                    response = requests.request(method, url, proxies=proxies, timeout=10, **kwargs)
                
                # 检查是否被封IP (常见的封IP状态码)
                if response.status_code in [403, 429, 503]:
                    raise requests.exceptions.RequestException("IP可能被封")
                
                return response
                
            except Exception as e:
                self.logger.warning(f"请求失败 (代理: {self.current_proxy}): {e}")
                
                with self.lock:
                    self.failed_proxies.add(self.current_proxy)
                
                if self.auto_switch or attempt < self.max_retries - 1:
                    self.current_proxy = self.get_working_proxy()
                    if self.current_proxy:
                        self.logger.info(f"自动切换到新代理: {self.current_proxy}")
                        continue
                
                if attempt == self.max_retries - 1:
                    raise e
    
    def enable_auto_switch(self):
        """启用自动切换代理"""
        self.auto_switch = True
        self.logger.info("已启用自动代理切换")
    
    def disable_auto_switch(self):
        """禁用自动切换代理"""
        self.auto_switch = False
        self.logger.info("已禁用自动代理切换")