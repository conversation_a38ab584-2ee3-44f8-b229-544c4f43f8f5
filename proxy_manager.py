import requests
import random
import time
import threading
import re
import json
from urllib.parse import urljoin
from urllib.request import Proxy<PERSON><PERSON><PERSON>, build_opener, install_opener
import logging
from typing import Optional, List, Dict, Tuple

class ProxyManager:
    def __init__(self):
        self.proxies = []
        self.current_proxy = None
        self.failed_proxies = set()
        self.lock = threading.Lock()
        self.auto_switch = False
        self.max_retries = 3
        self.current_ip = None
        self.session = requests.Session()

        # 配置日志
        logging.basicConfig(
            level=logging.INFO,
            format='%(asctime)s - %(levelname)s - %(message)s'
        )
        self.logger = logging.getLogger(__name__)
    
    def get_current_ip(self, proxy: Optional[str] = None) -> Optional[str]:
        """获取当前IP地址"""
        ip_check_urls = [
            'http://httpbin.org/ip',
            'http://ip-api.com/json',
            'https://api.ipify.org?format=json',
            'http://checkip.amazonaws.com'
        ]

        for url in ip_check_urls:
            try:
                if proxy:
                    proxies = {
                        'http': f'http://{proxy}',
                        'https': f'http://{proxy}'
                    }
                    response = requests.get(url, proxies=proxies, timeout=10)
                else:
                    response = requests.get(url, timeout=10)

                if response.status_code == 200:
                    if 'httpbin.org' in url:
                        return response.json().get('origin', '').split(',')[0].strip()
                    elif 'ip-api.com' in url:
                        return response.json().get('query', '')
                    elif 'ipify.org' in url:
                        return response.json().get('ip', '')
                    elif 'amazonaws.com' in url:
                        return response.text.strip()
            except Exception as e:
                self.logger.debug(f"IP检查失败 {url}: {e}")
                continue

        return None

    def fetch_free_proxies(self):
        """从免费代理网站获取代理列表"""
        # 备用代理列表（一些常见的公共代理，仅用于测试）
        backup_proxies = [
            "************:8888",
            "************:8080",
            "***********:80",
            "************:8888",
            "***************:80",
            "***************:80",
            "***************:80",
            "***************:80"
        ]

        proxy_sources = [
            {
                'url': 'https://api.lumiproxy.com/web_v1/free-proxy/list?page_size=60&page=1&language=zh-hans',
                'parser': self._parse_lumiproxy_json
            },
            {
                'url': 'https://api.lumiproxy.com/web_v1/free-proxy/list?page_size=60&page=2&language=zh-hans',
                'parser': self._parse_lumiproxy_json
            },
            {
                'url': 'https://api.lumiproxy.com/web_v1/free-proxy/list?page_size=60&page=3&language=zh-hans',
                'parser': self._parse_lumiproxy_json
            },
            {
                'url': 'https://www.proxy-list.download/api/v1/get?type=http',
                'parser': self._parse_simple_list
            },
            {
                'url': 'https://api.proxyscrape.com/v2/?request=get&protocol=http&timeout=10000&country=all&format=textplain',
                'parser': self._parse_simple_list
            }
        ]

        new_proxies = []
        successful_sources = 0

        for source in proxy_sources:
            try:
                self.logger.info(f"正在从 {source['url']} 获取代理...")
                response = requests.get(source['url'], timeout=10, headers={
                    'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36'
                })
                if response.status_code == 200:
                    proxies = source['parser'](response.text)
                    if proxies:
                        new_proxies.extend(proxies)
                        successful_sources += 1
                        self.logger.info(f"从该源获取到 {len(proxies)} 个代理")
                    else:
                        self.logger.warning(f"该源返回空列表")
                else:
                    self.logger.warning(f"HTTP错误 {response.status_code}")
            except Exception as e:
                self.logger.warning(f"获取代理失败 {source['url']}: {e}")

        # 如果没有成功获取到代理，使用备用代理
        if not new_proxies:
            self.logger.warning("无法从网络获取代理，使用备用代理列表")
            new_proxies = backup_proxies.copy()

        # 去重并过滤
        unique_proxies = list(set(new_proxies))
        valid_proxies = [p for p in unique_proxies if self._is_valid_proxy_format(p)]

        with self.lock:
            self.proxies = valid_proxies
            self.failed_proxies.clear()

        self.logger.info(f"获取到 {len(self.proxies)} 个有效代理 (成功源: {successful_sources})")
        return len(self.proxies)

    def _parse_simple_list(self, text: str) -> List[str]:
        """解析简单的代理列表"""
        proxies = []
        for line in text.strip().split('\n'):
            line = line.strip()
            if line and ':' in line:
                proxies.append(line)
        return proxies

    def _parse_lumiproxy_json(self, text: str) -> List[str]:
        """解析LumiProxy JSON格式"""
        proxies = []
        try:
            import json
            data = json.loads(text)
            if data.get('code') == 200 and 'data' in data:
                proxy_list = data['data'].get('list', [])
                for proxy_info in proxy_list:
                    if isinstance(proxy_info, dict):
                        ip = proxy_info.get('ip')
                        port = proxy_info.get('port')
                        # 只选择HTTP代理 (protocol=2表示HTTP)
                        protocol = proxy_info.get('protocol', 0)
                        if ip and port and protocol == 2:
                            proxy = f"{ip}:{port}"
                            if self._is_valid_proxy_format(proxy):
                                proxies.append(proxy)
        except Exception as e:
            self.logger.debug(f"解析LumiProxy JSON失败: {e}")
        return proxies

    def _is_valid_proxy_format(self, proxy: str) -> bool:
        """验证代理格式是否正确"""
        pattern = r'^(\d{1,3}\.){3}\d{1,3}:\d{1,5}$'
        if not re.match(pattern, proxy):
            return False

        # 进一步验证IP地址和端口范围
        try:
            ip, port = proxy.split(':')
            port_num = int(port)

            # 验证端口范围
            if not (1 <= port_num <= 65535):
                return False

            # 验证IP地址各段
            ip_parts = ip.split('.')
            for part in ip_parts:
                num = int(part)
                if not (0 <= num <= 255):
                    return False

            return True
        except (ValueError, IndexError):
            return False
    
    def test_proxy(self, proxy: str, test_url: str = None, timeout: int = 5) -> bool:
        """测试代理是否可用"""
        # 使用简单快速的测试URL
        test_urls = [
            'http://www.baidu.com',
            'http://example.com',
            'http://httpbin.org/ip'
        ]

        if test_url:
            test_urls = [test_url]

        for url in test_urls[:2]:  # 只测试前2个URL，提高速度
            try:
                proxies = {
                    'http': f'http://{proxy}',
                    'https': f'http://{proxy}'
                }

                headers = {
                    'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36'
                }

                response = requests.get(url, proxies=proxies, timeout=timeout, headers=headers)
                if response.status_code in [200, 301, 302, 307, 308]:
                    self.logger.debug(f"代理 {proxy} 测试成功 (URL: {url}, 状态码: {response.status_code})")
                    return True

            except Exception as e:
                self.logger.debug(f"代理测试失败 {proxy} @ {url}: {e}")
                continue

        return False
    
    def get_working_proxy(self) -> Optional[str]:
        """获取一个可用的代理"""
        with self.lock:
            available_proxies = [p for p in self.proxies if p not in self.failed_proxies]

        if not available_proxies:
            self.logger.warning("没有可用代理，重新获取...")
            if self.fetch_free_proxies() == 0:
                self.logger.error("无法获取任何代理")
                return None
            with self.lock:
                available_proxies = [p for p in self.proxies if p not in self.failed_proxies]

        if not available_proxies:
            self.logger.error("所有代理都已失败，重置失败列表")
            with self.lock:
                self.failed_proxies.clear()
                available_proxies = self.proxies.copy()

        # 随机测试代理，快速找到可用的
        test_count = min(8, len(available_proxies))
        test_proxies = random.sample(available_proxies, test_count)

        self.logger.info(f"开始测试 {test_count} 个代理...")

        for i, proxy in enumerate(test_proxies, 1):
            self.logger.info(f"测试代理 {i}/{test_count}: {proxy}")
            if self.test_proxy(proxy):
                self.current_proxy = proxy
                # 获取代理的IP地址
                try:
                    proxy_ip = self.get_current_ip(proxy)
                    self.logger.info(f"✅ 找到可用代理: {proxy} (IP: {proxy_ip or '未知'})")
                except:
                    self.logger.info(f"✅ 找到可用代理: {proxy}")
                return proxy
            else:
                with self.lock:
                    self.failed_proxies.add(proxy)
                self.logger.debug(f"❌ 代理不可用: {proxy}")

        self.logger.warning("所有测试的代理都不可用")
        return None
    
    def switch_proxy(self) -> Tuple[Optional[str], Optional[str], Optional[str], Optional[str]]:
        """手动切换代理，返回(旧代理, 新代理, 旧IP, 新IP)"""
        old_proxy = self.current_proxy
        old_ip = None

        # 获取切换前的IP
        if old_proxy:
            old_ip = self.get_current_ip(old_proxy)
            self.logger.info(f"切换前 - 代理: {old_proxy}, IP: {old_ip}")

        # 标记当前代理为失败（如果存在）
        if old_proxy:
            with self.lock:
                self.failed_proxies.add(old_proxy)

        new_proxy = self.get_working_proxy()
        new_ip = None

        if new_proxy:
            new_ip = self.get_current_ip(new_proxy)
            self.logger.info(f"切换后 - 代理: {new_proxy}, IP: {new_ip}")
            self.logger.info(f"代理切换成功: {old_proxy} ({old_ip}) -> {new_proxy} ({new_ip})")
            return old_proxy, new_proxy, old_ip, new_ip
        else:
            self.logger.error("无法找到可用代理")
            return old_proxy, None, old_ip, None
    
    def make_request(self, url: str, method: str = 'GET', **kwargs) -> requests.Response:
        """使用代理发送请求，支持自动重试和切换"""
        for attempt in range(self.max_retries):
            if not self.current_proxy:
                self.current_proxy = self.get_working_proxy()

            if not self.current_proxy:
                raise Exception("无可用代理")

            try:
                proxies = {
                    'http': f'http://{self.current_proxy}',
                    'https': f'http://{self.current_proxy}'
                }

                # 设置默认超时
                if 'timeout' not in kwargs:
                    kwargs['timeout'] = 15

                self.logger.debug(f"使用代理 {self.current_proxy} 请求 {url}")

                if method.upper() == 'GET':
                    response = self.session.get(url, proxies=proxies, **kwargs)
                elif method.upper() == 'POST':
                    response = self.session.post(url, proxies=proxies, **kwargs)
                else:
                    response = self.session.request(method, url, proxies=proxies, **kwargs)

                # 检查是否被封IP (常见的封IP状态码)
                if response.status_code in [403, 429, 503, 520, 521, 522, 523, 524]:
                    raise requests.exceptions.RequestException(f"IP可能被封 (状态码: {response.status_code})")

                # 检查响应内容是否包含封IP的关键词
                if response.status_code == 200:
                    content_lower = response.text.lower()
                    blocked_keywords = ['blocked', 'forbidden', 'access denied', 'rate limit', 'too many requests']
                    if any(keyword in content_lower for keyword in blocked_keywords):
                        raise requests.exceptions.RequestException("响应内容表明IP被封")

                self.logger.debug(f"请求成功 (状态码: {response.status_code})")
                return response

            except Exception as e:
                self.logger.warning(f"请求失败 (代理: {self.current_proxy}, 尝试: {attempt + 1}/{self.max_retries}): {e}")

                # 标记当前代理为失败
                with self.lock:
                    self.failed_proxies.add(self.current_proxy)

                # 如果启用自动切换或还有重试机会，尝试切换代理
                if self.auto_switch or attempt < self.max_retries - 1:
                    old_proxy, new_proxy, old_ip, new_ip = self.switch_proxy()
                    if new_proxy:
                        self.logger.info(f"自动切换代理: {old_proxy} ({old_ip}) -> {new_proxy} ({new_ip})")
                        continue
                    else:
                        self.logger.error("无法找到新的可用代理")

                # 最后一次尝试失败，抛出异常
                if attempt == self.max_retries - 1:
                    raise e
    
    def enable_auto_switch(self):
        """启用自动切换代理"""
        self.auto_switch = True
        self.logger.info("已启用自动代理切换")

    def disable_auto_switch(self):
        """禁用自动切换代理"""
        self.auto_switch = False
        self.logger.info("已禁用自动代理切换")

    def get_proxy_stats(self) -> Dict:
        """获取代理统计信息"""
        with self.lock:
            total_proxies = len(self.proxies)
            failed_proxies = len(self.failed_proxies)
            available_proxies = total_proxies - failed_proxies

        return {
            'total': total_proxies,
            'failed': failed_proxies,
            'available': available_proxies,
            'current_proxy': self.current_proxy,
            'auto_switch': self.auto_switch
        }

    def reset_failed_proxies(self):
        """重置失败代理列表"""
        with self.lock:
            self.failed_proxies.clear()
        self.logger.info("已重置失败代理列表")

    def add_custom_proxy(self, proxy: str) -> bool:
        """添加自定义代理"""
        if not self._is_valid_proxy_format(proxy):
            self.logger.error(f"代理格式无效: {proxy}")
            return False

        if self.test_proxy(proxy):
            with self.lock:
                if proxy not in self.proxies:
                    self.proxies.append(proxy)
                    self.logger.info(f"成功添加自定义代理: {proxy}")
            return True
        else:
            self.logger.error(f"自定义代理不可用: {proxy}")
            return False