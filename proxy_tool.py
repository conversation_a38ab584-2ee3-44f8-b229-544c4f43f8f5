#!/usr/bin/env python3
import argparse
import json
import time
import sys
from typing import Optional
from proxy_manager import ProxyManager

class ProxyTool:
    def __init__(self):
        self.proxy_manager = ProxyManager()

    def print_banner(self):
        """打印工具横幅"""
        print("=" * 60)
        print("           🌐 代理网络访问工具 🌐")
        print("=" * 60)
        print("功能：自动获取免费代理，支持手动/自动切换")
        print("作者：AI Assistant")
        print("=" * 60)

    def print_menu(self):
        """打印菜单"""
        print("\n📋 功能菜单:")
        print("1. 🔄 获取代理列表")
        print("2. 🔍 测试当前代理")
        print("3. 🔀 手动切换代理")
        print("4. ⚙️  启用/禁用自动切换")
        print("5. 🌐 发送测试请求")
        print("6. 📊 批量请求测试")
        print("7. 📈 查看代理统计")
        print("8. 🔧 添加自定义代理")
        print("9. 🗑️  重置失败代理")
        print("0. 🚪 退出")

    def interactive_mode(self):
        """交互式模式"""
        self.print_banner()

        # 显示当前IP
        current_ip = self.proxy_manager.get_current_ip()
        if current_ip:
            print(f"🌍 当前IP地址: {current_ip}")
        else:
            print("⚠️  无法获取当前IP地址")

        while True:
            try:
                self.print_menu()
                choice = input("\n请选择操作 (0-9): ").strip()

                if choice == '0':
                    print("👋 感谢使用，再见！")
                    break
                elif choice == '1':
                    self.fetch_proxies()
                elif choice == '2':
                    self.test_current_proxy()
                elif choice == '3':
                    self.manual_switch_proxy()
                elif choice == '4':
                    self.toggle_auto_switch()
                elif choice == '5':
                    self.send_test_request()
                elif choice == '6':
                    self.batch_request_test()
                elif choice == '7':
                    self.show_proxy_stats()
                elif choice == '8':
                    self.add_custom_proxy()
                elif choice == '9':
                    self.reset_failed_proxies()
                else:
                    print("❌ 无效选择，请重试")

            except KeyboardInterrupt:
                print("\n\n👋 程序被中断，再见！")
                break
            except Exception as e:
                print(f"❌ 操作失败: {e}")

    def fetch_proxies(self):
        """获取代理列表"""
        print("\n🔄 正在获取代理列表...")
        count = self.proxy_manager.fetch_free_proxies()
        if count > 0:
            print(f"✅ 成功获取 {count} 个代理")
        else:
            print("❌ 未能获取到任何代理")

    def test_current_proxy(self):
        """测试当前代理"""
        print("\n🔍 测试当前代理...")
        if self.proxy_manager.current_proxy:
            print(f"📍 当前代理: {self.proxy_manager.current_proxy}")

            # 获取代理IP
            proxy_ip = self.proxy_manager.get_current_ip(self.proxy_manager.current_proxy)
            if proxy_ip:
                print(f"🌐 代理IP: {proxy_ip}")

            if self.proxy_manager.test_proxy(self.proxy_manager.current_proxy):
                print("✅ 代理可用")
            else:
                print("❌ 代理不可用")
        else:
            print("⚠️  当前没有设置代理")

    def manual_switch_proxy(self):
        """手动切换代理"""
        print("\n🔀 正在切换代理...")
        old_proxy, new_proxy, old_ip, new_ip = self.proxy_manager.switch_proxy()

        if new_proxy:
            print("✅ 代理切换成功！")
            print(f"   旧代理: {old_proxy or '无'} (IP: {old_ip or '未知'})")
            print(f"   新代理: {new_proxy} (IP: {new_ip or '未知'})")
        else:
            print("❌ 切换失败，无法找到可用代理")

    def toggle_auto_switch(self):
        """切换自动切换状态"""
        current_status = "启用" if self.proxy_manager.auto_switch else "禁用"
        print(f"\n⚙️  当前自动切换状态: {current_status}")
        toggle = input("是否切换状态? (y/n): ").lower()
        if toggle == 'y':
            if self.proxy_manager.auto_switch:
                self.proxy_manager.disable_auto_switch()
                print("✅ 已禁用自动代理切换")
            else:
                self.proxy_manager.enable_auto_switch()
                print("✅ 已启用自动代理切换")

    def send_test_request(self):
        """发送测试请求"""
        print("\n🌐 发送测试请求...")
        url = input("请输入测试URL (默认: http://httpbin.org/ip): ").strip()
        if not url:
            url = "http://httpbin.org/ip"

        try:
            print(f"📡 正在请求: {url}")
            if self.proxy_manager.current_proxy:
                print(f"📍 使用代理: {self.proxy_manager.current_proxy}")

            response = self.proxy_manager.make_request(url)
            print(f"✅ 请求成功 (状态码: {response.status_code})")

            # 尝试解析IP信息
            try:
                if 'httpbin.org/ip' in url:
                    ip_info = response.json()
                    print(f"🌐 返回IP: {ip_info.get('origin', '未知')}")
                else:
                    print(f"📄 响应内容: {response.text[:200]}...")
            except:
                print(f"📄 响应内容: {response.text[:200]}...")

        except Exception as e:
            print(f"❌ 请求失败: {e}")

    def show_proxy_stats(self):
        """显示代理统计"""
        print("\n📈 代理统计信息:")
        stats = self.proxy_manager.get_proxy_stats()
        print(f"   总代理数: {stats['total']}")
        print(f"   可用代理: {stats['available']}")
        print(f"   失败代理: {stats['failed']}")
        print(f"   当前代理: {stats['current_proxy'] or '无'}")
        print(f"   自动切换: {'启用' if stats['auto_switch'] else '禁用'}")

    def add_custom_proxy(self):
        """添加自定义代理"""
        print("\n🔧 添加自定义代理...")
        proxy = input("请输入代理地址 (格式: IP:端口): ").strip()
        if proxy:
            if self.proxy_manager.add_custom_proxy(proxy):
                print(f"✅ 成功添加代理: {proxy}")
            else:
                print(f"❌ 添加失败: {proxy}")
        else:
            print("❌ 代理地址不能为空")

    def reset_failed_proxies(self):
        """重置失败代理"""
        print("\n🗑️  重置失败代理列表...")
        self.proxy_manager.reset_failed_proxies()
        print("✅ 已重置失败代理列表")
    
    def batch_request_test(self):
        """批量请求测试"""
        print("\n📊 批量请求测试...")
        url = input("请输入测试URL: ").strip()
        if not url:
            print("❌ URL不能为空")
            return

        # 确保URL有协议前缀
        if not url.startswith(('http://', 'https://')):
            url = 'http://' + url
            print(f"🔧 自动添加协议前缀: {url}")

        try:
            count = int(input("请输入请求次数 (默认10): ") or "10")
            interval = float(input("请输入请求间隔秒数 (默认1): ") or "1")
        except ValueError:
            print("❌ 输入格式错误")
            return

        # 询问是否启用自动切换
        auto_switch = input("是否启用自动代理切换? (y/n, 默认y): ").lower()
        if auto_switch != 'n':
            self.proxy_manager.enable_auto_switch()
            print("✅ 已启用自动代理切换")

        # 确保有代理可用
        print("\n🔄 检查代理状态...")
        if not self.proxy_manager.proxies:
            print("📥 正在获取代理列表...")
            proxy_count = self.proxy_manager.fetch_free_proxies()
            if proxy_count == 0:
                print("❌ 无法获取代理，测试终止")
                return
            print(f"✅ 获取到 {proxy_count} 个代理")

        # 确保有可用代理
        if not self.proxy_manager.current_proxy:
            print("🔍 正在寻找可用代理...")
            working_proxy = self.proxy_manager.get_working_proxy()
            if not working_proxy:
                print("❌ 无法找到可用代理，测试终止")
                return

        success_count = 0
        proxy_switches = 0
        last_proxy = self.proxy_manager.current_proxy

        print(f"\n🚀 开始批量请求测试 ({count} 次)...")
        print("=" * 50)

        for i in range(count):
            try:
                current_proxy = self.proxy_manager.current_proxy
                if current_proxy != last_proxy:
                    proxy_switches += 1
                    print(f"🔄 代理已切换: {last_proxy} -> {current_proxy}")
                    last_proxy = current_proxy

                print(f"📡 请求 {i+1}/{count} (代理: {current_proxy or '无'})")
                response = self.proxy_manager.make_request(url)
                print(f"   ✅ 成功 (状态码: {response.status_code})")
                success_count += 1

                # 如果是IP检查URL，显示返回的IP
                if 'httpbin.org/ip' in url or 'ipify' in url:
                    try:
                        ip_data = response.json()
                        ip = ip_data.get('origin', ip_data.get('ip', '未知'))
                        print(f"   🌐 返回IP: {ip}")
                    except:
                        pass

            except Exception as e:
                print(f"   ❌ 失败: {e}")

            if i < count - 1:
                time.sleep(interval)

        print("=" * 50)
        print(f"📊 测试完成:")
        print(f"   成功率: {success_count}/{count} ({success_count/count*100:.1f}%)")
        print(f"   代理切换次数: {proxy_switches}")
        print(f"   最终代理: {self.proxy_manager.current_proxy or '无'}")

def main():
    parser = argparse.ArgumentParser(
        description='🌐 代理网络访问工具 - 自动获取免费代理，支持手动/自动切换',
        formatter_class=argparse.RawDescriptionHelpFormatter,
        epilog="""
使用示例:
  python proxy_tool.py                           # 交互式模式
  python proxy_tool.py --url http://httpbin.org/ip  # 单次请求
  python proxy_tool.py --url http://example.com --count 10 --auto  # 批量请求
        """
    )
    parser.add_argument('--url', help='要访问的URL')
    parser.add_argument('--auto', action='store_true', help='启用自动代理切换')
    parser.add_argument('--count', type=int, default=1, help='请求次数 (默认: 1)')
    parser.add_argument('--interval', type=float, default=1, help='请求间隔秒数 (默认: 1)')
    parser.add_argument('--proxy', help='使用指定代理 (格式: IP:端口)')
    parser.add_argument('--show-ip', action='store_true', help='显示IP地址信息')

    args = parser.parse_args()

    tool = ProxyTool()

    if args.url:
        # 命令行模式
        print("🌐 代理网络访问工具 - 命令行模式")
        print("=" * 50)

        # 显示当前IP
        if args.show_ip:
            current_ip = tool.proxy_manager.get_current_ip()
            print(f"🌍 当前IP: {current_ip or '未知'}")

        # 使用指定代理
        if args.proxy:
            if tool.proxy_manager.add_custom_proxy(args.proxy):
                tool.proxy_manager.current_proxy = args.proxy
                print(f"📍 使用指定代理: {args.proxy}")
            else:
                print(f"❌ 指定代理不可用: {args.proxy}")
                return
        else:
            print("🔄 正在获取代理...")
            count = tool.proxy_manager.fetch_free_proxies()
            if count == 0:
                print("❌ 无法获取代理，程序退出")
                return
            print(f"✅ 获取到 {count} 个代理")

        if args.auto:
            tool.proxy_manager.enable_auto_switch()
            print("⚙️  已启用自动代理切换")

        success_count = 0
        proxy_switches = 0
        last_proxy = tool.proxy_manager.current_proxy

        print(f"\n🚀 开始请求 {args.url}")
        print("=" * 50)

        for i in range(args.count):
            try:
                current_proxy = tool.proxy_manager.current_proxy
                if current_proxy != last_proxy:
                    proxy_switches += 1
                    print(f"🔄 代理切换: {last_proxy} -> {current_proxy}")
                    last_proxy = current_proxy

                print(f"📡 请求 {i+1}/{args.count} (代理: {current_proxy or '无'})")
                response = tool.proxy_manager.make_request(args.url)
                print(f"   ✅ 成功 (状态码: {response.status_code})")

                # 显示IP信息
                if args.show_ip or 'httpbin.org/ip' in args.url or 'ipify' in args.url:
                    try:
                        ip_data = response.json()
                        ip = ip_data.get('origin', ip_data.get('ip', '未知'))
                        print(f"   🌐 返回IP: {ip}")
                    except:
                        pass

                success_count += 1
            except Exception as e:
                print(f"   ❌ 失败: {e}")

            if i < args.count - 1:
                time.sleep(args.interval)

        print("=" * 50)
        print(f"📊 完成: {success_count}/{args.count} 成功 ({success_count/args.count*100:.1f}%)")
        if proxy_switches > 0:
            print(f"🔄 代理切换次数: {proxy_switches}")
    else:
        # 交互式模式
        tool.interactive_mode()

if __name__ == "__main__":
    try:
        main()
    except KeyboardInterrupt:
        print("\n\n👋 程序被中断，再见！")
        sys.exit(0)
    except Exception as e:
        print(f"\n❌ 程序出错: {e}")
        sys.exit(1)