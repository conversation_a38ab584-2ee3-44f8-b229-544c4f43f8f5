#!/usr/bin/env python3
import argparse
import json
import time
from proxy_manager import ProxyManager

class ProxyTool:
    def __init__(self):
        self.proxy_manager = ProxyManager()
    
    def interactive_mode(self):
        """交互式模式"""
        print("=== 代理网络访问工具 ===")
        print("1. 获取代理列表")
        print("2. 测试当前代理")
        print("3. 手动切换代理")
        print("4. 启用/禁用自动切换")
        print("5. 发送测试请求")
        print("6. 批量请求测试")
        print("0. 退出")
        
        while True:
            try:
                choice = input("\n请选择操作 (0-6): ").strip()
                
                if choice == '0':
                    break
                elif choice == '1':
                    print("正在获取代理列表...")
                    count = self.proxy_manager.fetch_free_proxies()
                    print(f"成功获取 {count} 个代理")
                
                elif choice == '2':
                    if self.proxy_manager.current_proxy:
                        print(f"当前代理: {self.proxy_manager.current_proxy}")
                        if self.proxy_manager.test_proxy(self.proxy_manager.current_proxy):
                            print("✓ 代理可用")
                        else:
                            print("✗ 代理不可用")
                    else:
                        print("当前没有设置代理")
                
                elif choice == '3':
                    print("正在切换代理...")
                    new_proxy = self.proxy_manager.switch_proxy()
                    if new_proxy:
                        print(f"✓ 已切换到: {new_proxy}")
                    else:
                        print("✗ 切换失败")
                
                elif choice == '4':
                    current_status = "启用" if self.proxy_manager.auto_switch else "禁用"
                    print(f"当前自动切换状态: {current_status}")
                    toggle = input("是否切换状态? (y/n): ").lower()
                    if toggle == 'y':
                        if self.proxy_manager.auto_switch:
                            self.proxy_manager.disable_auto_switch()
                        else:
                            self.proxy_manager.enable_auto_switch()
                
                elif choice == '5':
                    url = input("请输入测试URL (默认: http://httpbin.org/ip): ").strip()
                    if not url:
                        url = "http://httpbin.org/ip"
                    
                    try:
                        response = self.proxy_manager.make_request(url)
                        print(f"✓ 请求成功 (状态码: {response.status_code})")
                        print(f"响应内容: {response.text[:200]}...")
                    except Exception as e:
                        print(f"✗ 请求失败: {e}")
                
                elif choice == '6':
                    self.batch_request_test()
                
                else:
                    print("无效选择，请重试")
                    
            except KeyboardInterrupt:
                print("\n程序被中断")
                break
            except Exception as e:
                print(f"操作失败: {e}")
    
    def batch_request_test(self):
        """批量请求测试"""
        url = input("请输入测试URL: ").strip()
        if not url:
            print("URL不能为空")
            return
        
        try:
            count = int(input("请输入请求次数 (默认10): ") or "10")
            interval = float(input("请输入请求间隔秒数 (默认1): ") or "1")
        except ValueError:
            print("输入格式错误")
            return
        
        self.proxy_manager.enable_auto_switch()
        success_count = 0
        
        print(f"\n开始批量请求测试 ({count} 次)...")
        for i in range(count):
            try:
                print(f"请求 {i+1}/{count} (代理: {self.proxy_manager.current_proxy})")
                response = self.proxy_manager.make_request(url)
                print(f"  ✓ 成功 (状态码: {response.status_code})")
                success_count += 1
            except Exception as e:
                print(f"  ✗ 失败: {e}")
            
            if i < count - 1:
                time.sleep(interval)
        
        print(f"\n测试完成: {success_count}/{count} 成功")

def main():
    parser = argparse.ArgumentParser(description='代理网络访问工具')
    parser.add_argument('--url', help='要访问的URL')
    parser.add_argument('--auto', action='store_true', help='启用自动代理切换')
    parser.add_argument('--count', type=int, default=1, help='请求次数')
    parser.add_argument('--interval', type=float, default=1, help='请求间隔(秒)')
    
    args = parser.parse_args()
    
    tool = ProxyTool()
    
    if args.url:
        # 命令行模式
        if args.auto:
            tool.proxy_manager.enable_auto_switch()
        
        print("正在获取代理...")
        tool.proxy_manager.fetch_free_proxies()
        
        success_count = 0
        for i in range(args.count):
            try:
                print(f"请求 {i+1}/{args.count}")
                response = tool.proxy_manager.make_request(args.url)
                print(f"✓ 成功 (状态码: {response.status_code})")
                success_count += 1
            except Exception as e:
                print(f"✗ 失败: {e}")
            
            if i < args.count - 1:
                time.sleep(args.interval)
        
        print(f"完成: {success_count}/{args.count} 成功")
    else:
        # 交互式模式
        tool.interactive_mode()

if __name__ == "__main__":
    main()