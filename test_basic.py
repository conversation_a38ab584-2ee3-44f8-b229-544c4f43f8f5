#!/usr/bin/env python3
"""
基础测试脚本 - 测试代理管理器的基本功能
"""

import sys
import requests
from proxy_manager import ProxyManager

def test_direct_connection():
    """测试直接连接"""
    print("=== 测试直接连接 ===")
    try:
        response = requests.get("http://httpbin.org/ip", timeout=10)
        if response.status_code == 200:
            ip_data = response.json()
            print(f"✅ 直接连接成功")
            print(f"🌐 当前IP: {ip_data.get('origin', '未知')}")
            return True
        else:
            print(f"❌ 直接连接失败，状态码: {response.status_code}")
            return False
    except Exception as e:
        print(f"❌ 直接连接异常: {e}")
        return False

def test_proxy_manager_basic():
    """测试代理管理器基础功能"""
    print("\n=== 测试代理管理器基础功能 ===")
    
    try:
        # 创建代理管理器
        proxy_manager = ProxyManager()
        print("✅ 代理管理器创建成功")
        
        # 测试获取当前IP
        print("🔍 测试获取当前IP...")
        current_ip = proxy_manager.get_current_ip()
        if current_ip:
            print(f"✅ 获取当前IP成功: {current_ip}")
        else:
            print("⚠️  无法获取当前IP")
        
        # 测试获取代理
        print("🔄 测试获取代理列表...")
        count = proxy_manager.fetch_free_proxies()
        print(f"📊 获取到 {count} 个代理")
        
        if count > 0:
            print("✅ 代理获取成功")
            
            # 显示前几个代理
            print("📋 前5个代理:")
            for i, proxy in enumerate(proxy_manager.proxies[:5]):
                print(f"   {i+1}. {proxy}")
            
            # 测试代理切换
            print("\n🔀 测试代理切换...")
            old_proxy, new_proxy, old_ip, new_ip = proxy_manager.switch_proxy()
            if new_proxy:
                print(f"✅ 代理切换成功")
                print(f"   旧代理: {old_proxy or '无'} (IP: {old_ip or '未知'})")
                print(f"   新代理: {new_proxy} (IP: {new_ip or '未知'})")
            else:
                print("❌ 代理切换失败")
        else:
            print("❌ 未获取到任何代理")
            
        return True
        
    except Exception as e:
        print(f"❌ 代理管理器测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_proxy_request():
    """测试通过代理发送请求"""
    print("\n=== 测试代理请求 ===")
    
    try:
        proxy_manager = ProxyManager()
        
        # 获取代理
        count = proxy_manager.fetch_free_proxies()
        if count == 0:
            print("❌ 没有可用代理，跳过代理请求测试")
            return False
        
        # 尝试发送请求
        print("📡 尝试通过代理发送请求...")
        try:
            response = proxy_manager.make_request("http://httpbin.org/ip")
            if response.status_code == 200:
                ip_data = response.json()
                print(f"✅ 代理请求成功")
                print(f"🌐 代理IP: {ip_data.get('origin', '未知')}")
                print(f"📍 使用代理: {proxy_manager.current_proxy}")
                return True
            else:
                print(f"❌ 代理请求失败，状态码: {response.status_code}")
                return False
        except Exception as e:
            print(f"❌ 代理请求异常: {e}")
            return False
            
    except Exception as e:
        print(f"❌ 代理请求测试失败: {e}")
        return False

def main():
    """主测试函数"""
    print("🧪 代理工具基础测试")
    print("=" * 50)
    
    # 测试结果
    results = []
    
    # 测试直接连接
    results.append(("直接连接", test_direct_connection()))
    
    # 测试代理管理器
    results.append(("代理管理器", test_proxy_manager_basic()))
    
    # 测试代理请求
    results.append(("代理请求", test_proxy_request()))
    
    # 显示测试结果
    print("\n" + "=" * 50)
    print("📊 测试结果汇总:")
    for test_name, success in results:
        status = "✅ 通过" if success else "❌ 失败"
        print(f"   {test_name}: {status}")
    
    # 总体结果
    passed = sum(1 for _, success in results if success)
    total = len(results)
    print(f"\n🎯 总体结果: {passed}/{total} 测试通过")
    
    if passed == total:
        print("🎉 所有测试通过！")
        return 0
    else:
        print("⚠️  部分测试失败，请检查网络连接和代理源")
        return 1

if __name__ == "__main__":
    try:
        exit_code = main()
        sys.exit(exit_code)
    except KeyboardInterrupt:
        print("\n\n👋 测试被中断")
        sys.exit(1)
    except Exception as e:
        print(f"\n❌ 测试出现未预期错误: {e}")
        import traceback
        traceback.print_exc()
        sys.exit(1)
