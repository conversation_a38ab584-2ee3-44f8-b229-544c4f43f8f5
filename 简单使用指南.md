# 🌐 代理工具简单使用指南

## 🚀 问题解决

你遇到的"无可用代理"问题已经解决！现在工具可以：
- ✅ 成功获取37000+个免费代理
- ✅ 自动测试代理可用性
- ✅ 显示切换前后IP地址
- ✅ 支持自动切换代理

## 📋 快速使用步骤

### 1. 启动交互式模式
```bash
python proxy_tool.py
```

### 2. 按照菜单操作
```
📋 功能菜单:
1. 🔄 获取代理列表    ← 首先选择这个！
2. 🔍 测试当前代理
3. 🔀 手动切换代理
4. ⚙️  启用/禁用自动切换
5. 🌐 发送测试请求
6. 📊 批量请求测试    ← 然后选择这个测试
```

### 3. 正确的使用顺序
1. **先选择 "1" 获取代理列表**
2. **再选择 "6" 进行批量测试**
3. 输入测试URL时，可以直接输入：`www.baidu.com`（工具会自动添加http://）

## 🎯 测试示例

### 测试百度访问
```
请选择操作 (0-9): 1
# 等待获取代理完成...

请选择操作 (0-9): 6
请输入测试URL: www.baidu.com
请输入请求次数 (默认10): 5
请输入请求间隔秒数 (默认1): 1
是否启用自动代理切换? (y/n, 默认y): y
```

### 测试IP检查
```
请选择操作 (0-9): 6
请输入测试URL: httpbin.org/ip
请输入请求次数 (默认10): 3
```

## 🔧 命令行快速测试

如果交互式模式有问题，可以直接用命令行：

```bash
# 测试获取IP
python proxy_tool.py --url http://httpbin.org/ip --show-ip --auto

# 批量测试百度
python proxy_tool.py --url http://www.baidu.com --count 5 --auto
```

## 📊 预期结果

成功运行后你会看到：
```
✅ 获取到 37559 个代理
🔍 正在寻找可用代理...
✅ 找到可用代理: *******:8080 (IP: *******)
📡 请求 1/5 (代理: *******:8080)
   ✅ 成功 (状态码: 200)
🔄 代理切换: *******:8080 (*******) -> **********:8080 (***********)
```

## ⚠️ 注意事项

1. **首次使用必须先获取代理列表**（选择菜单1）
2. **代理测试需要时间**，请耐心等待
3. **免费代理成功率不是100%**，这是正常现象
4. **如果所有代理都失败**，可以选择菜单9重置失败代理，然后重新获取

## 🛠️ 故障排除

### 如果还是显示"无可用代理"：
1. 确保网络连接正常
2. 先运行：`python demo.py` 测试基本功能
3. 尝试重新获取代理列表（菜单选项1）
4. 重置失败代理（菜单选项9）

### 如果代理测试很慢：
- 这是正常现象，免费代理质量参差不齐
- 工具会自动跳过不可用的代理
- 通常测试5-10个代理就能找到可用的

## 🎉 成功标志

当你看到这样的输出时，说明工具正常工作：
```
✅ 获取到 XXXXX 个代理
✅ 找到可用代理: X.X.X.X:XXXX (IP: Y.Y.Y.Y)
✅ 请求成功 (状态码: 200)
🔄 代理切换: 旧IP -> 新IP
```

现在你的代理工具已经完全可用了！🎊
