#!/usr/bin/env python3
"""
简单测试脚本 - 测试代理工具的基本功能
"""

import sys
import time
from proxy_manager import ProxyManager

def test_proxy_manager_creation():
    """测试代理管理器创建"""
    print("🧪 测试1: 代理管理器创建")
    try:
        proxy_manager = ProxyManager()
        print("✅ 代理管理器创建成功")
        return True, proxy_manager
    except Exception as e:
        print(f"❌ 代理管理器创建失败: {e}")
        return False, None

def test_proxy_format_validation():
    """测试代理格式验证"""
    print("\n🧪 测试2: 代理格式验证")
    proxy_manager = ProxyManager()
    
    test_cases = [
        ("192.168.1.1:8080", True),
        ("8.8.8.8:80", True),
        ("invalid_proxy", False),
        ("192.168.1.1", False),
        ("192.168.1.1:99999", False),
        ("999.999.999.999:8080", False)
    ]
    
    all_passed = True
    for proxy, expected in test_cases:
        result = proxy_manager._is_valid_proxy_format(proxy)
        if result == expected:
            print(f"✅ {proxy}: {result} (预期: {expected})")
        else:
            print(f"❌ {proxy}: {result} (预期: {expected})")
            all_passed = False
    
    return all_passed

def test_proxy_management():
    """测试代理管理功能"""
    print("\n🧪 测试3: 代理管理功能")
    proxy_manager = ProxyManager()
    
    # 添加测试代理
    test_proxies = ["1.2.3.4:8080", "5.6.7.8:8080", "9.10.11.12:8080"]
    proxy_manager.proxies = test_proxies.copy()
    
    print(f"✅ 添加了 {len(test_proxies)} 个测试代理")
    
    # 测试统计功能
    stats = proxy_manager.get_proxy_stats()
    print(f"📊 统计信息: 总数={stats['total']}, 可用={stats['available']}, 失败={stats['failed']}")
    
    # 测试失败代理管理
    proxy_manager.failed_proxies.add("1.2.3.4:8080")
    stats_after = proxy_manager.get_proxy_stats()
    print(f"📊 标记失败后: 总数={stats_after['total']}, 可用={stats_after['available']}, 失败={stats_after['failed']}")
    
    # 重置失败代理
    proxy_manager.reset_failed_proxies()
    stats_reset = proxy_manager.get_proxy_stats()
    print(f"📊 重置后: 总数={stats_reset['total']}, 可用={stats_reset['available']}, 失败={stats_reset['failed']}")
    
    return True

def test_auto_switch_toggle():
    """测试自动切换开关"""
    print("\n🧪 测试4: 自动切换开关")
    proxy_manager = ProxyManager()
    
    # 测试启用
    proxy_manager.enable_auto_switch()
    print(f"✅ 启用自动切换: {proxy_manager.auto_switch}")
    
    # 测试禁用
    proxy_manager.disable_auto_switch()
    print(f"✅ 禁用自动切换: {proxy_manager.auto_switch}")
    
    return True

def test_proxy_switching_simulation():
    """测试代理切换模拟"""
    print("\n🧪 测试5: 代理切换模拟")
    proxy_manager = ProxyManager()
    
    # 添加测试代理
    test_proxies = ["192.168.1.1:8080", "192.168.1.2:8080", "192.168.1.3:8080"]
    proxy_manager.proxies = test_proxies.copy()
    
    print("🔄 模拟代理切换...")
    for i in range(3):
        # 模拟切换（不进行实际网络测试）
        old_proxy = proxy_manager.current_proxy
        if proxy_manager.proxies:
            # 选择一个不同的代理
            available = [p for p in proxy_manager.proxies if p != old_proxy]
            if available:
                new_proxy = available[0]
                proxy_manager.current_proxy = new_proxy
                print(f"   切换 {i+1}: {old_proxy or '无'} -> {new_proxy}")
            else:
                print(f"   切换 {i+1}: 无可用代理")
        time.sleep(0.5)
    
    return True

def main():
    """主测试函数"""
    print("🚀 代理工具简单测试")
    print("=" * 50)
    
    tests = [
        ("代理管理器创建", test_proxy_manager_creation),
        ("代理格式验证", test_proxy_format_validation),
        ("代理管理功能", test_proxy_management),
        ("自动切换开关", test_auto_switch_toggle),
        ("代理切换模拟", test_proxy_switching_simulation)
    ]
    
    results = []
    proxy_manager = None
    
    for test_name, test_func in tests:
        try:
            if test_name == "代理管理器创建":
                success, proxy_manager = test_func()
            else:
                success = test_func()
            results.append((test_name, success))
        except Exception as e:
            print(f"❌ 测试 '{test_name}' 出现异常: {e}")
            results.append((test_name, False))
    
    # 显示测试结果
    print("\n" + "=" * 50)
    print("📊 测试结果汇总:")
    passed = 0
    for test_name, success in results:
        status = "✅ 通过" if success else "❌ 失败"
        print(f"   {test_name}: {status}")
        if success:
            passed += 1
    
    total = len(results)
    print(f"\n🎯 总体结果: {passed}/{total} 测试通过 ({passed/total*100:.1f}%)")
    
    if passed == total:
        print("🎉 所有基础功能测试通过！")
        print("\n💡 接下来可以尝试:")
        print("   1. python proxy_tool.py --help  # 查看帮助")
        print("   2. python proxy_tool.py         # 交互式模式")
        print("   3. 在有网络的环境中测试实际代理功能")
        return 0
    else:
        print("⚠️  部分测试失败，请检查代码")
        return 1

if __name__ == "__main__":
    try:
        exit_code = main()
        sys.exit(exit_code)
    except KeyboardInterrupt:
        print("\n\n👋 测试被中断")
        sys.exit(1)
    except Exception as e:
        print(f"\n❌ 测试出现未预期错误: {e}")
        import traceback
        traceback.print_exc()
        sys.exit(1)
