#!/usr/bin/env python3
"""
代理工具演示脚本 - 展示主要功能
"""

import time
import random
from proxy_manager import ProxyMana<PERSON>

def demo_proxy_manager():
    """演示代理管理器功能"""
    print("🌐 代理网络访问工具演示")
    print("=" * 60)
    
    # 创建代理管理器
    print("1. 创建代理管理器...")
    proxy_manager = ProxyManager()
    print("✅ 代理管理器创建成功")
    
    # 模拟添加一些代理（用于演示）
    print("\n2. 添加示例代理...")
    demo_proxies = [
        "8.8.8.8:8080",
        "1.1.1.1:8080", 
        "9.9.9.9:8080",
        "208.67.222.222:8080",
        "208.67.220.220:8080"
    ]
    
    # 直接添加到代理列表（跳过网络验证用于演示）
    proxy_manager.proxies = demo_proxies.copy()
    print(f"✅ 添加了 {len(demo_proxies)} 个示例代理")
    
    # 显示代理列表
    print("\n3. 当前代理列表:")
    for i, proxy in enumerate(proxy_manager.proxies, 1):
        print(f"   {i}. {proxy}")
    
    # 演示代理切换
    print("\n4. 演示代理切换...")
    for i in range(3):
        # 模拟选择一个随机代理
        if proxy_manager.proxies:
            old_proxy = proxy_manager.current_proxy
            new_proxy = random.choice(proxy_manager.proxies)
            proxy_manager.current_proxy = new_proxy
            
            # 模拟IP地址
            old_ip = f"192.168.{random.randint(1,255)}.{random.randint(1,255)}" if old_proxy else None
            new_ip = f"10.{random.randint(1,255)}.{random.randint(1,255)}.{random.randint(1,255)}"
            
            print(f"   切换 {i+1}: {old_proxy or '无'} ({old_ip or '无'}) -> {new_proxy} ({new_ip})")
            time.sleep(1)
    
    # 演示统计信息
    print("\n5. 代理统计信息:")
    stats = proxy_manager.get_proxy_stats()
    print(f"   总代理数: {stats['total']}")
    print(f"   可用代理: {stats['available']}")
    print(f"   失败代理: {stats['failed']}")
    print(f"   当前代理: {stats['current_proxy'] or '无'}")
    print(f"   自动切换: {'启用' if stats['auto_switch'] else '禁用'}")
    
    # 演示自动切换功能
    print("\n6. 演示自动切换功能...")
    proxy_manager.enable_auto_switch()
    print("✅ 已启用自动代理切换")
    
    # 模拟请求失败和自动切换
    print("\n7. 模拟网络请求和自动切换:")
    for i in range(5):
        current_proxy = proxy_manager.current_proxy
        print(f"   请求 {i+1} - 使用代理: {current_proxy}")
        
        # 模拟随机失败
        if random.random() < 0.3:  # 30% 失败率
            print(f"   ❌ 请求失败，IP可能被封")
            # 模拟切换代理
            if proxy_manager.proxies:
                available = [p for p in proxy_manager.proxies if p != current_proxy]
                if available:
                    new_proxy = random.choice(available)
                    old_ip = f"10.{random.randint(1,255)}.{random.randint(1,255)}.{random.randint(1,255)}"
                    new_ip = f"172.{random.randint(16,31)}.{random.randint(1,255)}.{random.randint(1,255)}"
                    proxy_manager.current_proxy = new_proxy
                    print(f"   🔄 自动切换: {current_proxy} ({old_ip}) -> {new_proxy} ({new_ip})")
        else:
            print(f"   ✅ 请求成功")
        
        time.sleep(0.5)
    
    print("\n8. 演示完成!")
    print("=" * 60)
    print("🎉 代理工具主要功能演示完成")
    print("\n📋 主要特性:")
    print("   ✅ 自动获取免费代理")
    print("   ✅ 手动/自动代理切换") 
    print("   ✅ IP地址显示")
    print("   ✅ 智能故障检测")
    print("   ✅ 详细统计信息")
    print("   ✅ 自定义代理支持")
    
    print("\n🚀 使用方法:")
    print("   python proxy_tool.py                    # 交互式模式")
    print("   python proxy_tool.py --url <URL>        # 命令行模式")
    print("   python proxy_tool.py --help             # 查看帮助")

def demo_command_examples():
    """演示命令行使用示例"""
    print("\n" + "=" * 60)
    print("📖 命令行使用示例")
    print("=" * 60)
    
    examples = [
        {
            "desc": "获取IP地址",
            "cmd": "python proxy_tool.py --url http://httpbin.org/ip --show-ip"
        },
        {
            "desc": "批量请求，自动切换代理",
            "cmd": "python proxy_tool.py --url http://example.com --count 10 --auto"
        },
        {
            "desc": "使用指定代理",
            "cmd": "python proxy_tool.py --url http://httpbin.org/ip --proxy *******:8080"
        },
        {
            "desc": "高频请求测试",
            "cmd": "python proxy_tool.py --url http://api.example.com --count 50 --interval 0.5 --auto"
        }
    ]
    
    for i, example in enumerate(examples, 1):
        print(f"{i}. {example['desc']}:")
        print(f"   {example['cmd']}")
        print()

if __name__ == "__main__":
    try:
        demo_proxy_manager()
        demo_command_examples()
        
        print("\n💡 提示:")
        print("   - 在实际使用中，工具会自动从网络获取真实的免费代理")
        print("   - 支持多种代理源，自动验证代理可用性")
        print("   - 当检测到IP被封时，会自动切换到新的代理")
        print("   - 所有操作都有详细的日志输出")
        
    except KeyboardInterrupt:
        print("\n\n👋 演示被中断")
    except Exception as e:
        print(f"\n❌ 演示出错: {e}")
        import traceback
        traceback.print_exc()
