#!/usr/bin/env python3
"""
快速测试脚本 - 测试代理获取和基本功能
"""

import sys
from proxy_manager import ProxyManager

def quick_test():
    """快速测试代理功能"""
    print("🧪 快速代理测试")
    print("=" * 40)
    
    # 创建代理管理器
    proxy_manager = ProxyManager()
    
    # 测试获取代理
    print("1. 📥 获取代理列表...")
    try:
        count = proxy_manager.fetch_free_proxies()
        print(f"✅ 获取到 {count} 个代理")
        
        if count > 0:
            # 显示前几个代理
            print("📋 代理列表预览:")
            for i, proxy in enumerate(proxy_manager.proxies[:5], 1):
                print(f"   {i}. {proxy}")
            
            # 测试寻找可用代理
            print("\n2. 🔍 寻找可用代理...")
            working_proxy = proxy_manager.get_working_proxy()
            
            if working_proxy:
                print(f"✅ 找到可用代理: {working_proxy}")
                
                # 测试简单请求
                print("\n3. 🌐 测试代理请求...")
                try:
                    # 使用简单的URL进行测试
                    test_urls = ['http://www.baidu.com', 'http://example.com']
                    
                    for test_url in test_urls:
                        try:
                            print(f"   测试 {test_url}...")
                            response = proxy_manager.make_request(test_url)
                            print(f"   ✅ 成功 (状态码: {response.status_code})")
                            break
                        except Exception as e:
                            print(f"   ❌ 失败: {e}")
                            continue
                    
                except Exception as e:
                    print(f"❌ 请求测试失败: {e}")
            else:
                print("❌ 未找到可用代理")
        else:
            print("❌ 未获取到任何代理")
            
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()
    
    # 显示统计
    print("\n4. 📊 代理统计:")
    stats = proxy_manager.get_proxy_stats()
    for key, value in stats.items():
        print(f"   {key}: {value}")
    
    print("\n" + "=" * 40)
    print("🎯 测试完成")

if __name__ == "__main__":
    try:
        quick_test()
    except KeyboardInterrupt:
        print("\n\n👋 测试被中断")
    except Exception as e:
        print(f"\n❌ 测试出错: {e}")
        sys.exit(1)
