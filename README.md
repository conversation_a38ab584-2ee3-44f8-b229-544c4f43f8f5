# 🌐 代理网络访问工具

一个功能强大的Python代理网络访问工具，支持自动获取免费代理、手动/自动切换代理，并能显示切换前后的IP地址。

## ✨ 主要功能

- 🔄 **自动获取免费代理**: 从多个免费代理网站获取可用代理
- 🔀 **智能代理切换**: 支持手动切换和自动切换代理
- 🌐 **IP地址显示**: 显示切换前后的IP地址
- 🛡️ **自动故障恢复**: 当IP被封或代理失效时自动切换
- 📊 **详细统计信息**: 显示代理使用统计和成功率
- 🎯 **多种使用模式**: 支持交互式模式和命令行模式
- ⚙️ **自定义代理**: 支持添加自定义代理服务器

## 🚀 快速开始

### 安装依赖

```bash
pip install -r requirements.txt
```

### 交互式模式

```bash
python proxy_tool.py
```

### 命令行模式

```bash
# 单次请求
python proxy_tool.py --url http://httpbin.org/ip

# 批量请求，启用自动切换
python proxy_tool.py --url http://example.com --count 10 --auto

# 显示IP信息
python proxy_tool.py --url http://httpbin.org/ip --show-ip

# 使用指定代理
python proxy_tool.py --url http://httpbin.org/ip --proxy *******:8080
```

## 📖 使用示例

### 基础使用

```python
from proxy_manager import ProxyManager

# 创建代理管理器
proxy_manager = ProxyManager()

# 获取代理列表
count = proxy_manager.fetch_free_proxies()
print(f"获取到 {count} 个代理")

# 手动切换代理
old_proxy, new_proxy, old_ip, new_ip = proxy_manager.switch_proxy()
print(f"切换: {old_proxy} ({old_ip}) -> {new_proxy} ({new_ip})")

# 发送请求
response = proxy_manager.make_request("http://httpbin.org/ip")
print(response.json())
```

### 自动切换模式

```python
# 启用自动切换
proxy_manager.enable_auto_switch()

# 发送请求，如果失败会自动切换代理重试
try:
    response = proxy_manager.make_request("http://example.com")
    print("请求成功")
except Exception as e:
    print(f"请求失败: {e}")
```

### 批量请求

```python
# 批量请求，自动处理代理切换
urls = ["http://httpbin.org/ip", "http://httpbin.org/get"]
for url in urls:
    try:
        response = proxy_manager.make_request(url)
        print(f"✓ {url} 成功")
    except Exception as e:
        print(f"✗ {url} 失败: {e}")
```

## 🎮 交互式菜单

运行 `python proxy_tool.py` 进入交互式模式：

```
📋 功能菜单:
1. 🔄 获取代理列表
2. 🔍 测试当前代理
3. 🔀 手动切换代理
4. ⚙️  启用/禁用自动切换
5. 🌐 发送测试请求
6. 📊 批量请求测试
7. 📈 查看代理统计
8. 🔧 添加自定义代理
9. 🗑️  重置失败代理
0. 🚪 退出
```

## 🔧 配置选项

### ProxyManager 参数

- `max_retries`: 最大重试次数 (默认: 3)
- `auto_switch`: 是否启用自动切换 (默认: False)

### 代理源

工具从以下免费代理源获取代理：

- proxy-list.download
- proxyscrape.com
- GitHub 代理列表项目
- 其他免费代理API

## 📊 功能特性

### IP地址显示
- 显示当前真实IP
- 显示代理服务器IP
- 切换前后IP对比

### 智能故障检测
- HTTP状态码检测 (403, 429, 503等)
- 响应内容关键词检测
- 连接超时检测

### 代理管理
- 自动去重和格式验证
- 失败代理黑名单
- 代理可用性测试

## 🛠️ 高级用法

### 自定义代理源

```python
# 添加自定义代理
proxy_manager.add_custom_proxy("*******:8080")

# 重置失败代理列表
proxy_manager.reset_failed_proxies()

# 获取统计信息
stats = proxy_manager.get_proxy_stats()
```

### 请求配置

```python
# 自定义请求参数
response = proxy_manager.make_request(
    "http://example.com",
    method="POST",
    headers={"User-Agent": "Custom Agent"},
    timeout=30
)
```

## 📝 注意事项

1. **免费代理限制**: 免费代理可能不稳定，建议用于测试环境
2. **请求频率**: 避免过于频繁的请求，以免被目标网站封禁
3. **代理质量**: 工具会自动测试代理可用性，但无法保证所有代理都稳定
4. **网络环境**: 某些网络环境可能限制代理使用

## 🤝 贡献

欢迎提交Issue和Pull Request来改进这个工具！

## 📄 许可证

MIT License

## 🔗 相关链接

- [requests 文档](https://docs.python-requests.org/)
- [免费代理列表](https://github.com/clarketm/proxy-list)
