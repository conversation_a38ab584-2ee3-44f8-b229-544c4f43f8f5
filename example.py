#!/usr/bin/env python3
"""
代理网络访问工具使用示例
演示如何使用ProxyManager进行网络请求
"""

import time
from proxy_manager import ProxyManager

def example_basic_usage():
    """基础使用示例"""
    print("=== 基础使用示例 ===")
    
    # 创建代理管理器
    proxy_manager = ProxyManager()
    
    # 获取代理列表
    print("1. 获取代理列表...")
    count = proxy_manager.fetch_free_proxies()
    print(f"获取到 {count} 个代理")
    
    # 获取当前IP（不使用代理）
    current_ip = proxy_manager.get_current_ip()
    print(f"当前IP: {current_ip}")
    
    # 手动切换代理
    print("\n2. 手动切换代理...")
    old_proxy, new_proxy, old_ip, new_ip = proxy_manager.switch_proxy()
    if new_proxy:
        print(f"切换成功: {old_proxy} ({old_ip}) -> {new_proxy} ({new_ip})")
    else:
        print("切换失败")
    
    # 发送请求
    print("\n3. 发送测试请求...")
    try:
        response = proxy_manager.make_request("http://httpbin.org/ip")
        print(f"请求成功: {response.json()}")
    except Exception as e:
        print(f"请求失败: {e}")

def example_auto_switch():
    """自动切换示例"""
    print("\n=== 自动切换示例 ===")
    
    proxy_manager = ProxyManager()
    proxy_manager.fetch_free_proxies()
    
    # 启用自动切换
    proxy_manager.enable_auto_switch()
    
    # 模拟频繁请求
    urls = [
        "http://httpbin.org/ip",
        "http://httpbin.org/user-agent",
        "http://httpbin.org/headers"
    ]
    
    for i, url in enumerate(urls * 3):  # 重复请求
        try:
            print(f"\n请求 {i+1}: {url}")
            print(f"当前代理: {proxy_manager.current_proxy}")
            
            response = proxy_manager.make_request(url)
            print(f"成功 (状态码: {response.status_code})")
            
            # 如果是IP检查，显示返回的IP
            if 'ip' in url:
                try:
                    ip_info = response.json()
                    print(f"返回IP: {ip_info.get('origin', '未知')}")
                except:
                    pass
                    
        except Exception as e:
            print(f"失败: {e}")
        
        time.sleep(1)  # 避免请求过快

def example_custom_proxy():
    """自定义代理示例"""
    print("\n=== 自定义代理示例 ===")
    
    proxy_manager = ProxyManager()
    
    # 添加自定义代理（这里使用示例地址，实际使用时请替换为真实代理）
    custom_proxies = [
        "*******:8080",  # 示例代理，通常不可用
        "*******:8080",  # 示例代理，通常不可用
    ]
    
    for proxy in custom_proxies:
        success = proxy_manager.add_custom_proxy(proxy)
        print(f"添加代理 {proxy}: {'成功' if success else '失败'}")
    
    # 显示统计信息
    stats = proxy_manager.get_proxy_stats()
    print(f"\n代理统计: {stats}")

def example_batch_requests():
    """批量请求示例"""
    print("\n=== 批量请求示例 ===")
    
    proxy_manager = ProxyManager()
    proxy_manager.fetch_free_proxies()
    proxy_manager.enable_auto_switch()
    
    # 批量请求不同的URL
    test_urls = [
        "http://httpbin.org/ip",
        "http://httpbin.org/get",
        "http://httpbin.org/user-agent"
    ]
    
    success_count = 0
    total_requests = len(test_urls) * 2
    
    for round_num in range(2):
        print(f"\n--- 第 {round_num + 1} 轮请求 ---")
        for i, url in enumerate(test_urls):
            try:
                print(f"请求: {url}")
                response = proxy_manager.make_request(url)
                print(f"  ✓ 成功 (代理: {proxy_manager.current_proxy})")
                success_count += 1
            except Exception as e:
                print(f"  ✗ 失败: {e}")
            
            time.sleep(0.5)
    
    print(f"\n批量请求完成: {success_count}/{total_requests} 成功")

if __name__ == "__main__":
    try:
        # 运行所有示例
        example_basic_usage()
        example_auto_switch()
        example_custom_proxy()
        example_batch_requests()
        
        print("\n=== 所有示例完成 ===")
        
    except KeyboardInterrupt:
        print("\n程序被中断")
    except Exception as e:
        print(f"\n示例运行出错: {e}")
