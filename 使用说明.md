# 🌐 代理网络访问工具 - 使用说明

## 📦 文件说明

- `proxy_manager.py` - 核心代理管理器类
- `proxy_tool.py` - 主要工具脚本（交互式和命令行模式）
- `example.py` - 编程使用示例
- `demo.py` - 功能演示脚本
- `test_basic.py` - 基础测试脚本
- `requirements.txt` - 依赖包列表

## 🚀 快速开始

### 1. 安装依赖
```bash
pip install -r requirements.txt
```

### 2. 运行演示
```bash
python demo.py
```

### 3. 交互式使用
```bash
python proxy_tool.py
```

### 4. 命令行使用
```bash
# 获取当前IP
python proxy_tool.py --url http://httpbin.org/ip --show-ip

# 批量请求
python proxy_tool.py --url http://example.com --count 10 --auto
```

## 🎮 交互式菜单功能

运行 `python proxy_tool.py` 后可以使用以下功能：

1. **🔄 获取代理列表** - 从免费代理网站自动获取可用代理
2. **🔍 测试当前代理** - 测试当前代理是否可用，显示代理IP
3. **🔀 手动切换代理** - 手动切换到新的可用代理，显示切换前后IP
4. **⚙️ 启用/禁用自动切换** - 控制是否在请求失败时自动切换代理
5. **🌐 发送测试请求** - 发送HTTP请求测试代理功能
6. **📊 批量请求测试** - 批量发送请求，测试代理稳定性
7. **📈 查看代理统计** - 显示代理使用统计信息
8. **🔧 添加自定义代理** - 手动添加自定义代理服务器
9. **🗑️ 重置失败代理** - 清空失败代理黑名单

## 💻 编程接口使用

### 基础使用
```python
from proxy_manager import ProxyManager

# 创建代理管理器
proxy_manager = ProxyManager()

# 获取代理列表
count = proxy_manager.fetch_free_proxies()
print(f"获取到 {count} 个代理")

# 手动切换代理
old_proxy, new_proxy, old_ip, new_ip = proxy_manager.switch_proxy()
print(f"切换: {old_proxy} ({old_ip}) -> {new_proxy} ({new_ip})")

# 发送请求
response = proxy_manager.make_request("http://httpbin.org/ip")
print(response.json())
```

### 自动切换模式
```python
# 启用自动切换
proxy_manager.enable_auto_switch()

# 发送请求，失败时自动切换代理
try:
    response = proxy_manager.make_request("http://example.com")
    print("请求成功")
except Exception as e:
    print(f"请求失败: {e}")
```

## 🔧 命令行参数

```bash
python proxy_tool.py [选项]

选项:
  --url URL            要访问的URL
  --auto               启用自动代理切换
  --count COUNT        请求次数 (默认: 1)
  --interval INTERVAL  请求间隔秒数 (默认: 1)
  --proxy PROXY        使用指定代理 (格式: IP:端口)
  --show-ip            显示IP地址信息
  -h, --help           显示帮助信息
```

## 📊 主要功能特性

### 🔄 自动获取代理
- 从多个免费代理网站获取代理列表
- 自动去重和格式验证
- 支持多种代理源

### 🔀 智能代理切换
- 手动切换代理
- 自动故障检测和切换
- 显示切换前后的IP地址

### 🛡️ 故障检测
- HTTP状态码检测 (403, 429, 503等)
- 响应内容关键词检测
- 连接超时检测

### 📈 统计信息
- 代理总数统计
- 可用/失败代理统计
- 当前代理状态

## 🌐 代理源

工具从以下免费代理源获取代理：
- proxy-list.download
- proxyscrape.com
- GitHub 代理列表项目
- 其他免费代理API

## ⚠️ 注意事项

1. **网络环境**: 某些网络环境可能限制代理使用
2. **代理质量**: 免费代理可能不稳定，建议用于测试
3. **请求频率**: 避免过于频繁的请求
4. **合法使用**: 请遵守目标网站的使用条款

## 🔍 故障排除

### 无法获取代理
- 检查网络连接
- 尝试运行 `python demo.py` 测试基本功能
- 检查防火墙设置

### 代理连接失败
- 免费代理质量不稳定是正常现象
- 尝试多次获取代理列表
- 使用自动切换模式

### 请求超时
- 增加超时时间
- 检查目标网站是否可访问
- 尝试使用不同的代理

## 📝 使用示例

### 示例1: 检查IP地址
```bash
python proxy_tool.py --url http://httpbin.org/ip --show-ip
```

### 示例2: 批量测试
```bash
python proxy_tool.py --url http://httpbin.org/ip --count 5 --auto --interval 2
```

### 示例3: 使用自定义代理
```bash
python proxy_tool.py --url http://httpbin.org/ip --proxy *******:8080
```

## 🎯 最佳实践

1. **首次使用**: 先运行 `python demo.py` 了解功能
2. **测试环境**: 使用交互式模式进行测试
3. **生产环境**: 使用编程接口集成到项目中
4. **监控**: 定期检查代理统计信息
5. **备用方案**: 准备多个代理源和自定义代理

## 🤝 技术支持

如果遇到问题：
1. 查看日志输出了解详细错误信息
2. 运行测试脚本检查基本功能
3. 检查网络连接和防火墙设置
4. 尝试使用不同的代理源

---

🎉 **祝您使用愉快！**
